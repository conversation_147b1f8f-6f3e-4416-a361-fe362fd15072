package Controllers;

import Models.User;
import Services.UserService;
import jakarta.servlet.*;
import jakarta.servlet.http.*;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.servlet.annotation.WebServlet;
import Models.MemberLevel;

@WebServlet({ "/user", "/addUser", "/editUser", "/updateUserStatus" })
public class UserController extends HttpServlet {
    private final UserService userService = new UserService();

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp)
            throws ServletException, IOException {
        String servletPath = req.getServletPath();
        if ("/user".equals(servletPath)) {
            // Hiển thị danh sách Member
            List<User> allUsers = userService.getAllUsers();
            // Lọc chỉ lấy Member
            List<User> members = allUsers.stream()
                    .filter(user -> "Member".equals(user.getRole()))
                    .collect(Collectors.toList());
            req.setAttribute("userList", members);
            req.getRequestDispatcher("/user.jsp").forward(req, resp);
        } else if ("/addUser".equals(servletPath)) {
            // Hiển thị form thêm Member
            req.getRequestDispatcher("/addUser.jsp").forward(req, resp);
        } else if ("/editUser".equals(servletPath)) {
            // Hiển thị form chỉnh sửa Member
            String idStr = req.getParameter("id");
            if (idStr != null) {
                int id = Integer.parseInt(idStr);
                User user = userService.getUserById(id);
                req.setAttribute("user", user);
            }
            req.getRequestDispatcher("/editUser.jsp").forward(req, resp);
        } else if ("/updateUserStatus".equals(servletPath)) {
            // Cập nhật trạng thái Member (GET)
            String idStr = req.getParameter("id");
            String status = req.getParameter("status");
            if (idStr != null && status != null) {
                int id = Integer.parseInt(idStr);
                boolean updated = userService.updateUserStatus(id, status);
                HttpSession session = req.getSession();
                if (updated) {
                    session.setAttribute("successMessage", "Cập nhật trạng thái Member thành công!");
                } else {
                    session.setAttribute("errorMessage", "Cập nhật trạng thái Member thất bại!");
                }
            }
            resp.sendRedirect(req.getContextPath() + "/user");
        }
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp)
            throws ServletException, IOException {
        String servletPath = req.getServletPath();
        if ("/addUser".equals(servletPath)) {
            // Xử lý thêm Member
            User user = new User();
            user.setEmail(req.getParameter("email"));
            user.setUserName(req.getParameter("userName"));
            user.setFullName(req.getParameter("fullName"));
            user.setPhoneNumber(req.getParameter("phoneNumber"));
            user.setAddress(req.getParameter("address"));
            user.setGender(req.getParameter("gender"));
            user.setRole("Member"); // Luôn là Member
            user.setStatus("Active"); // luôn là Active khi tạo mới
            String dobStr = req.getParameter("dob");
            if (dobStr != null && !dobStr.isEmpty()) {
                user.setDob(java.time.LocalDate.parse(dobStr));
            }
            String rawPassword = req.getParameter("password");
            MemberLevel defaultLevel = new MemberLevel();
            defaultLevel.setId(1);
            user.setLevel(defaultLevel);
            boolean created = userService.createUser(user, rawPassword);
            HttpSession session = req.getSession();
            if (created) {
                session.setAttribute("successMessage", "Thêm Member mới thành công!");
                resp.sendRedirect(req.getContextPath() + "/user");
            } else {
                req.setAttribute("errorMessage", "Tạo Member thất bại. Vui lòng kiểm tra lại dữ liệu!");
                req.getRequestDispatcher("/addUser.jsp").forward(req, resp);
            }
        } else if ("/editUser".equals(servletPath)) {
            // Xử lý cập nhật Member
            String idStr = req.getParameter("id");
            if (idStr != null) {
                int id = Integer.parseInt(idStr);
                User user = userService.getUserById(id);
                user.setEmail(req.getParameter("email"));
                user.setUserName(req.getParameter("userName"));
                user.setFullName(req.getParameter("fullName"));
                user.setPhoneNumber(req.getParameter("phoneNumber"));
                user.setAddress(req.getParameter("address"));
                user.setGender(req.getParameter("gender"));
                user.setRole("Member"); // Luôn là Member
                user.setStatus(req.getParameter("status"));
                String dobStr = req.getParameter("dob");
                if (dobStr != null && !dobStr.isEmpty()) {
                    user.setDob(java.time.LocalDate.parse(dobStr));
                }
                // Đảm bảo luôn có level
                MemberLevel defaultLevel = new MemberLevel();
                defaultLevel.setId(1);
                user.setLevel(defaultLevel);

                // Kiểm tra xem có cập nhật mật khẩu không
                String newPassword = req.getParameter("password");
                boolean updated;
                if (newPassword != null && !newPassword.isEmpty()) {
                    updated = userService.updateUserWithPassword(user, newPassword);
                } else {
                    updated = userService.updateUser(user);
                }

                HttpSession session = req.getSession();
                if (updated) {
                    session.setAttribute("successMessage", "Cập nhật thông tin Member thành công!");
                    resp.sendRedirect(req.getContextPath() + "/user");
                } else {
                    req.setAttribute("user", user);
                    req.setAttribute("errorMessage", "Cập nhật Member thất bại. Vui lòng kiểm tra lại dữ liệu!");
                    req.getRequestDispatcher("/editUser.jsp").forward(req, resp);
                }
            } else {
                resp.sendRedirect(req.getContextPath() + "/user");
            }
        } else if ("/updateUserStatus".equals(servletPath)) {
            // Cập nhật trạng thái Member (POST)
            String idStr = req.getParameter("id");
            String status = req.getParameter("status");
            if (idStr != null && status != null) {
                int id = Integer.parseInt(idStr);
                boolean updated = userService.updateUserStatus(id, status);
                HttpSession session = req.getSession();
                if (updated) {
                    session.setAttribute("successMessage", "Cập nhật trạng thái Member thành công!");
                } else {
                    session.setAttribute("errorMessage", "Cập nhật trạng thái Member thất bại!");
                }
            }
            resp.sendRedirect(req.getContextPath() + "/user");
        }
    }
}