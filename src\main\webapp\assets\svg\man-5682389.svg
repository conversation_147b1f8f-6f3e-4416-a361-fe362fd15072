<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version= "1.1" width="1350px" height="1352px" viewBox="-216 19 1350 1352">
<g id="layer1">
<g style="fill:rgb(115,211,255); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M-218.37926,13.96179L460.67877,13.962291L1139.7368,13.9627905L1139.7368,1372.078Q460.67877,1372.0785,-218.37926,1372.079Q-218.37926,693.0204,-218.37926,13.96179"/>
</g>
<g style="fill:rgb(0,0,0); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M484.9171,230.66629L484.51593,229.54974Q480.3209,217.73645,467.70367,209.43222Q455.74063,200.5885,439.6413,198.19975Q408.48712,195.9443,389.27945,219.28496Q373.2034,238.1959,372.98334,262.0152Q373.29086,272.18936,377.81253,283.18533Q382.9721,295.988,396.0591,317.06235L395.66245,333.5739L484.9171,230.66629"/>
</g>
<g style="fill:rgb(255,178,152); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M262.45477,557.15594L264.81247,556.5033Q280.34473,551.0814,301.04517,560.17126Q330.4129,573.03125,346.3825,590.51385Q351.76776,596.3417,359.0418,606.81165L337.82513,634.61Q476.51428,683.83276,478.77676,687.77356Q512.6753,703.4598,518.729,722.9829Q524.3789,739.17285,492.77472,774.1701L366.4199,872.5614Q347.21173,871.0884,335.18048,846.9081Q390.01697,774.25024,432.13098,745.6478L323.74994,724.6157L262.45477,557.15594"/>
</g>
<g style="fill:rgb(58,105,116); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M214.46445,634.8259L215.39322,638.26416Q227.89151,688.25476,259.16348,708.8437Q275.68063,718.4751,285.64764,720.20886Q291.75275,721.7984,298.23352,723.0353L337.27847,729.7566Q349.79187,732.1881,359.89395,736.2223Q375.84143,741.7463,401.3279,762.4998L451.1812,667.87946Q412.26202,640.5649,369.75235,625.554L340.431,564.2758L214.46445,634.8259"/>
</g>
<g style="fill:rgb(255,178,152); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M392.5694,344.94406L392.5704,344.9456L395.80206,323.97766L395.4721,316.00104C401.3294,315.78604,405.94974,314.46472,409.17532,311.83987C412.1981,309.38052,414.51093,305.65634,415.95718,300.7634C417.1888,296.5953,417.52786,293.51575,417.46033,291.6077C417.41644,290.371,417.46143,289.13876,417.3091,287.926C416.33463,280.17227,415.8989,276.09335,416.12225,275.6726C416.01144,273.68784,416.3058,271.16418,417.90485,268.59442C421.57675,262.69226,425.62372,260.99106,429.3514,262.56372C430.33307,262.9783,431.56555,264.15155,433.18607,266.26282C433.7367,266.98126,435.58936,270.7159,438.72617,277.58267C439.49054,279.25552,440.3078,279.8668,440.68387,279.31735C441.40192,278.26773,441.66922,276.9649,441.85474,275.59793C443.2022,265.6594,444.2316,258.91577,445.0007,255.38634C446.4124,248.91052,447.6525,245.26341,448.02786,244.16743C450.19308,237.84305,454.06085,232.10278,461.0415,228.69124C471.30515,223.67613,479.4067,224.17873,484.61108,228.59099C489.04034,240.86165,491.18347,248.96149,491.52817,252.86005C491.91064,257.18283,490.85754,261.39716,487.88217,265.55615C496.6701,278.70697,500.66516,286.7974,500.96313,289.78583C501.33112,293.4787,498.32504,295.31006,490.8617,295.44226C492.11105,300.84515,492.3934,304.23636,492.05356,305.71017C491.67438,307.35538,490.31842,308.62158,487.63446,309.4394C489.57755,309.7741,490.3705,311.14584,489.86404,313.52502C489.41583,315.6315,487.95178,319.09988,485.6194,323.9636C486.1316,329.6497,486.0706,333.82715,485.08057,336.36734C484.1233,338.82333,482.8656,340.17166,481.65674,340.5582C479.86255,341.86707,475.2001,342.60318,467.66916,342.76785L454.90857,338.43582L438.80246,365.6579L392.5694,344.94406 Z"/>
</g>
<g style="fill:rgb(0,0,0); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M457.5776,185.67076L457.57883,185.6723L457.5776,185.67076L457.57883,185.6723"/>
</g>
<g style="fill:rgb(0,0,0); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M465.05276,266.37292L465.54333,265.7842Q468.70374,261.83542,471.23108,260.39114Q471.79468,260.0766,472.3801,259.84555Q472.662,259.68964,474.17593,259.30475Q475.69962,258.95526,478.56558,258.79666Q474.53528,260.51523,472.25995,261.81705Q470.03082,263.04092,465.05276,266.37292"/>
</g>
<g style="fill:rgb(0,0,0); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M472.37427,276.49677L472.374,276.49866L472.374,276.49866L472.37427,276.49677"/>
</g>
<g style="fill:rgb(255,255,255); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M469.89346,269.95413L469.8933,269.95496L475.93256,263.27914Q477.39673,264.2707,477.86606,265.13165Q478.38214,265.92682,478.88852,268.24667L469.89346,269.95413"/>
</g>
<g style="fill:rgb(0,0,0); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M475.86218,263.25067L475.16525,266.76114L478.65643,268.20886Q478.61856,266.89307,478.49695,266.24197Q478.36188,265.602,478.30426,265.49826Q478.22064,265.26596,478.1045,265.05844Q477.98755,264.85278,477.8385,264.6718Q477.77975,264.56903,477.33264,264.17636Q476.87213,263.79446,475.86218,263.25067"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M630.37854,654.4857L630.37854,654.4857L630.37854,654.4867L618.0393,646.4349C663.94617,626.2374,707.5087,606.93604,731.78217,583.6938L731.78217,583.6938C755.22107,561.2506,760.8685,534.70764,766.9118,505.30832C767.89,500.54956,772.5407,497.48477,777.2995,498.46298C782.0583,499.4412,785.12305,504.09192,784.14484,508.85068L784.14484,508.85068C778.1835,537.8515,771.8257,569.7094,743.94965,596.4012L743.94965,596.4012C716.9075,622.2945,669.84424,642.8633,625.1244,662.5385C622.4041,663.73535,619.2635,663.4777,616.7746,661.85364C614.2857,660.22955,612.78516,657.4586,612.78516,654.4867L612.78516,654.4867L612.78516,654.4857C612.78516,649.62744,616.7236,645.689,621.58185,645.689C626.4401,645.689,630.37854,649.62744,630.37854,654.4857 Z"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M621.03015,646.4719L621.03015,646.4719L621.03015,646.4729L608.69257,638.42035L609.00696,638.2821L609.0053,638.28284C654.80145,618.1339,698.2184,598.8666,722.4338,575.68L722.4338,575.68C745.8729,553.2367,751.52014,526.694,757.5634,497.29456C758.5416,492.5358,763.1923,489.471,767.9511,490.44922C772.7099,491.42743,775.77466,496.07816,774.79645,500.8369L774.79645,500.8369C768.8351,529.8378,762.4775,561.6955,734.60126,588.3874L734.60126,588.3874C707.6195,614.2228,660.711,634.75476,616.0904,654.3865L616.08875,654.3872L615.77435,654.52545C613.0557,655.72156,609.9151,655.4639,607.4262,653.83984C604.9373,652.21576,603.43677,649.4448,603.43677,646.4729L603.43677,646.4729L603.43677,646.4719C603.43677,641.61365,607.3752,637.67523,612.23346,637.67523C617.09174,637.67523,621.03015,641.61365,621.03015,646.4719 Z"/>
</g>
<g style="fill:rgb(255,178,169); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M703.7411,671.6374L703.7411,671.6378Q703.7411,671.63916,703.7411,671.6374"/>
</g>
<g style="fill:rgb(255,178,152); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M620.7483,600.20764L636.1486,609.6955L648.9658,608.3147L667.49756,604.11426L689.7079,592.05176Q691.92444,590.8608,693.7717,590.0539Q698.0913,587.81976,702.04224,589.6277Q704.30554,591.21045,704.31177,592.4127Q704.42053,594.9884,702.78577,596.31604Q698.9283,600.0955,685.73883,606.12695Q703.8147,614.1726,707.4211,617.5509Q711.99664,621.0443,710.2854,626.7424L698.2476,633.19305Q696.8694,639.6208,694.74445,641.1501Q693.35333,642.2659,689.15625,641.0978Q686.5773,645.88477,684.0547,646.5754Q682.0183,647.02997,678.1237,645.14Q674.4762,650.0044,671.70544,650.0012Q663.37885,650.5798,663.0737,640.52966Q651.53754,647.12744,643.7625,645.5949Q640.9294,645.24963,636.26935,642.4846Q633.11224,640.8346,625.1817,633.30725Q621.77637,630.0384,613.6343,621.132L620.7483,600.20764"/>
</g>
<g style="fill:rgb(255,178,152); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M268.91986,559.5132L273.23962,553.58936Q323.22052,485.57056,333.3837,460.0336Q341.11032,442.36777,343.26196,434.30692Q345.6702,428.27063,356.09064,380.77466Q358.53842,371.3224,361.77612,364.85013Q365.09277,358.52945,368.86792,354.75217L385.8676,341.3786Q388.02487,339.60638,394.34915,330.69916L447.29724,352.17453Q435.92987,366.44376,440.7515,375.99936Q442.77435,380.62497,447.34616,386.2146Q469.58533,412.3083,470.98813,415.0794Q477.72974,424.38608,484.25568,435.0535Q506.63586,472.09344,518.2835,500.1808Q526.1914,500.80536,540.4646,514.4022Q552.8964,525.6285,598.3987,574.6504L631.8288,607.1885L618.8476,626.2407L571.4917,593.44006L491.5121,534.4991Q456.09705,503.94666,437.01324,464.47476Q444.9137,468.27185,441.9403,478.46768Q440.20236,487.18292,422.87903,521.2075Q397.20648,548.4298,353.59625,597.17957L268.91986,559.5132"/>
</g>
<g style="fill:rgb(255,255,255); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M359.41415,883.068L359.22067,881.321Q358.3125,873.43365,359.43515,869.97766Q359.86526,867.38385,363.07904,864.6244Q373.4447,858.86285,380.31653,864.83453Q381.13782,865.97076,381.45056,867.5194Q381.8554,868.0922,381.4757,872.9059Q380.8764,877.8975,376.94977,889.19214L390.28265,925.8221Q400.10956,935.9861,404.25375,941.9467Q407.75333,946.35486,408.9221,952.9562Q409.6303,961.24554,403.35626,966.9904Q396.5888,972.37506,388.04517,970.7411Q380.0618,971.17706,348.87164,941.31726L327.78036,896.1079L325.17905,853.49817Q332.5688,840.0167,336.1348,836.4662Q339.72382,832.34644,346.6441,830.48706L365.03406,875.2986L359.41415,883.068"/>
</g>
<g style="fill:rgb(83,93,102); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M353.79675,991.68506L374.17743,987.4806L394.55807,983.2761L390.95532,965.81396Q370.57483,970.0194,350.19434,974.22485Q351.99554,982.95496,353.79675,991.68506"/>
</g>
<g style="fill:rgb(83,93,102); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M376.792,969.6312L387.38272,972.1987L397.97348,974.76624Q402.9924,954.05945,408.01135,933.3527Q397.42035,930.7862,386.82938,928.2196Q381.81067,948.9254,376.792,969.6312"/>
</g>
<g style="fill:rgb(32,44,54); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M366.25677,986.0246L543.6631,1007.2829L721.06946,1028.5411Q718.766,1047.7643,716.4625,1066.9874Q539.05615,1045.7301,361.64975,1024.4728Q363.95328,1005.2487,366.25677,986.0246"/>
</g>
<g style="fill:rgb(32,44,54); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M387.90814,1146.6165L556.0115,1164.411L724.11487,1182.2054Q730.5388,1121.5166,736.96277,1060.8278Q568.8594,1043.0342,400.75595,1025.2407Q394.33203,1085.9286,387.90814,1146.6165"/>
</g>
<g style="fill:rgb(60,70,79); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M736.8574,1196.2634L723.7119,1197.6172L710.5664,1198.971L736.8574,1196.2634"/>
</g>
<g style="fill:rgb(60,70,79); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M329.59665,454.3345L329.59662,454.335L329.59656,454.33548L329.59662,454.335L329.59665,454.3345L329.59662,454.335L329.59656,454.33548Q329.59662,454.335,329.59665,454.3345"/>
</g>
<g style="fill:rgb(60,70,79); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M329.59665,454.3345L329.59662,454.335L329.59656,454.33548L329.59662,454.335L329.59665,454.3345L329.59662,454.335L329.59656,454.33548Q329.59662,454.335,329.59665,454.3345"/>
</g>
<g style="fill:rgb(32,44,54); fill-opacity:0.99607843; stroke-linejoin: miter">
</g>
<g style="fill:rgb(32,44,54); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M305.91953,356.4364L305.9195,356.4369L305.91946,356.43738L305.9195,356.4369L305.91953,356.4364L305.9195,356.4369L305.91946,356.43738Q305.9195,356.4369,305.91953,356.4364"/>
</g>
<g style="fill:rgb(32,44,54); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M781.55396,1063.6469L779.3452,1081.5057Q767.74133,1135.2008,724.3254,1165.3547Q680.62305,1195.4661,624.2037,1186.157Q570.51086,1174.5529,540.3585,1131.1351Q530.704,1117.1057,524.85376,1100.8713L520.1032,1084.2517Q516.3781,1067.1638,517.3489,1048.8682Q521.90564,992.80975,561.2661,957.61786Q600.62665,922.42596,656.8412,924.14935Q712.8971,928.706,748.0873,968.068Q783.27747,1007.43,781.55396,1063.6469"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M737.2195,1071.7604L735.4027,1083.1746Q726.7545,1117.3818,698.21185,1135.7673Q669.4864,1154.1193,633.4785,1146.8832Q599.27203,1138.2349,580.88715,1109.6915Q575.0033,1100.4705,571.6107,1089.919L568.9327,1079.1448Q566.9233,1068.093,567.9552,1056.3707Q572.1327,1020.4889,598.18463,998.77893Q624.2366,977.0689,660.28235,979.43115Q696.1632,983.60846,717.8725,1009.661Q739.5818,1035.7136,737.2195,1071.7604"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M432.3902,1009.279L432.3902,1009.279C461.1668,977.04767,491.41678,943.0507,526.01624,918.896L526.01624,918.896C560.7827,894.62463,599.3628,880.6759,635.74603,867.4387C643.6639,864.558,652.4178,868.64136,655.2985,876.5592C658.17926,884.47705,654.0959,893.23096,646.17804,896.1117L646.17804,896.1117C609.2146,909.55994,574.44507,922.2981,543.48206,943.9142L543.48206,943.9142C512.3515,965.6472,484.48682,996.7415,455.1505,1029.5997C449.5391,1035.8848,439.89505,1036.431,433.60995,1030.8196C427.32486,1025.2081,426.77878,1015.5641,432.3902,1009.279 Z"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M671.6879,912.6669L671.6879,912.6669L671.6879,912.66785C671.6879,920.2647,666.0985,926.70404,658.5771,927.77216C651.05566,928.8403,643.8944,924.2118,641.77936,916.9153L641.7632,916.85895L641.7584,916.8423L641.7746,916.8986C627.742,868.489,613.1364,818.2783,608.50665,774.1602L608.50665,774.1602C603.82416,729.5395,609.3636,691.60156,614.5767,655.51715C615.7815,647.17816,623.51825,641.39465,631.85724,642.5994C640.1962,643.8042,645.97974,651.54095,644.77496,659.87994L644.77496,659.87994C639.53204,696.1703,634.6155,730.6075,638.85175,770.97577L638.85175,770.97577C643.1398,811.8369,656.82263,859.2187,671.08,908.40375L671.0962,908.4601L671.10095,908.47675L641.1762,912.66785L641.1762,912.6669C641.1762,904.2413,648.0065,897.411,656.43207,897.411C664.85767,897.411,671.6879,904.2413,671.6879,912.6669 Z"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M275.8528,1216.9615L511.9801,1210.9397L748.1073,1204.918Q747.73236,1190.2157,747.3574,1175.5135Q511.23016,1181.5363,275.1029,1187.5591Q275.47787,1202.2603,275.8528,1216.9615"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M191.45245,311.66086L191.45241,311.66138L191.45236,311.66187L191.45241,311.66138L191.45245,311.66086L191.45241,311.66138L191.45236,311.66187Q191.45241,311.66138,191.45245,311.66086"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M180.07466,308.12985L180.07458,308.13037L180.07452,308.13086L180.07458,308.13037L180.07466,308.12985L180.07458,308.13037L180.07452,308.13086Q180.07458,308.13037,180.07466,308.12985"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M361.17426,1140.9569L345.09457,1127.9293L329.0149,1114.9017Q295.4134,1156.3734,261.81192,1197.845Q277.89096,1210.8733,293.96997,1223.9016L361.17426,1140.9569"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M205.68465,815.06506L205.68462,815.06555L205.68459,815.06604L205.68462,815.06555L205.68465,815.06506L205.68462,815.06555L205.68459,815.06604Q205.68462,815.06555,205.68465,815.06506"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M205.68466,815.06506L205.68462,815.06555L205.68457,815.06604L205.68462,815.06555L205.68466,815.06506L205.68462,815.06555L205.68457,815.06604Q205.68462,815.06555,205.68466,815.06506"/>
</g>
<g style="fill:rgb(60,70,79); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M646.7761,898.64404L654.1155,896.93494L661.45483,895.2258Q695.51685,1041.4564,729.57886,1187.6869Q722.23975,1189.3969,714.9006,1191.1069L646.7761,898.64404"/>
</g>
<g style="fill:rgb(32,44,54); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M471.49664,1073.7872L469.67987,1085.2013Q461.03162,1119.4087,432.489,1137.7943Q403.76352,1156.1462,367.7556,1148.9102Q333.54913,1140.2617,315.16425,1111.7184Q309.2804,1102.4973,305.88785,1091.9457L303.20978,1081.1715Q301.20038,1070.1198,302.2323,1058.3975Q306.40973,1022.51575,332.46167,1000.8057Q358.51364,979.09564,394.5595,981.4579Q430.4403,985.63525,452.14966,1011.6878Q473.859,1037.7404,471.49664,1073.7872"/>
</g>
<g style="fill:rgb(60,70,79); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M447.16156,1069.7402L445.89227,1077.714Q439.85028,1101.6106,419.91016,1114.454Q399.8423,1127.2739,374.6871,1122.2185Q350.7904,1116.1766,337.94687,1096.2367Q333.83646,1089.7949,331.4665,1082.4237L329.59567,1074.897Q328.192,1067.1764,328.91296,1058.9874Q331.8316,1033.921,350.03177,1018.755Q368.23193,1003.589,393.4137,1005.2396Q418.48016,1008.1583,433.64615,1026.3584Q448.81216,1044.5586,447.16156,1069.7402"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M275.2859,720.97235L286.59958,718.21173L297.91327,715.4511Q335.74832,870.47894,373.58337,1025.5068Q362.26993,1028.2683,350.95648,1031.0298Q313.1212,876.0011,275.2859,720.97235"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M204.01576,688.40875L279.1512,688.40924L354.28668,688.4097Q354.28668,705.76154,354.28668,723.11334Q279.1512,723.11383,204.01576,723.1143Q204.01576,705.76154,204.01576,688.40875"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M185.65686,736.7227L240.3998,736.7232L295.14273,736.7237Q295.14273,748.1212,295.14273,759.51874Q240.3998,759.5192,185.65686,759.5198Q185.65686,748.1212,185.65686,736.7227"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M222.59961,750.0291L229.84949,750.3131L237.09937,750.5971Q236.71419,760.446,236.32901,770.2949Q229.07909,770.0119,221.82916,769.7289Q222.21439,759.87897,222.59961,750.0291"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M214.37747,767.0533L231.01587,767.7045L247.65427,768.3557Q247.49664,772.3862,247.33902,776.41675Q230.70056,775.76654,214.06212,775.1163Q214.21979,771.0848,214.37747,767.0533"/>
</g>
<g style="fill:rgb(22,23,24); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M116.78676,416.36557L116.786736,416.3661L116.78671,416.36658L116.786736,416.3661L116.78676,416.36557L116.786736,416.3661L116.78671,416.36658Q116.786736,416.3661,116.78676,416.36557"/>
</g>
<g style="fill:rgb(255,178,152); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M319.39532,640.9563L325.81516,654.9702Q345.74106,695.13495,346.72668,726.16174Q347.87903,743.0145,340.057,783.5509L337.33533,931.36743L368.62012,1109.7073Q360.85596,1124.0964,354.14343,1126.3806Q348.74017,1129.5452,329.86017,1126.0408Q318.25888,1082.196,310.37952,1058.7303Q308.03012,1048.459,287.46518,1004.1627Q283.367,994.34644,280.75555,982.44617Q278.25928,970.3075,277.5487,954.9557Q277.08542,956.20886,276.33615,889.5606Q276.23068,876.927,273.16888,851.254Q271.5581,836.3965,263.25482,796.52234L248.50934,733.62115Q229.5993,704.47705,221.26537,686.56976Q215.45581,673.01624,214.6445,667.717Q212.97519,660.46045,212.82935,652.96497Q213.91164,585.34454,278.77023,543.39526Q287.13638,551.51227,296.31992,556.9784L330.41434,573.8884Q337.85504,579.33057,359.90222,606.23804L319.39532,640.9563"/>
</g>
<g style="fill:rgb(255,255,255); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M360.61472,1124.9762L360.61472,1124.9772Q358.6348,1109.2935,360.97815,1103.8473Q362.29028,1100.3009,364.75824,1098.152Q366.90402,1095.9886,370.88116,1095.9742Q373.81342,1096.065,377.6886,1099.8462Q380.11398,1101.8396,387.33688,1112.7017L416.81757,1121.2058Q421.22763,1122.5175,424.8454,1124.0455Q432.2809,1127.1018,431.84512,1131.2296Q432.25873,1133.2052,427.3901,1139.9243L415.97668,1143.1461Q403.2296,1146.4504,396.21606,1146.9102Q389.02393,1147.6888,373.90475,1147.2Q363.0442,1153.9781,355.57126,1155.4796Q347.18588,1157.4607,335.0313,1156.6158Q329.6441,1156.2203,326.99045,1154.6644Q324.17865,1153.0947,323.09158,1148.498Q321.23618,1139.2996,323.2194,1113.7916Q331.2115,1120.3145,338.57812,1118.8204Q344.92313,1118.036,362.6333,1106.3876L360.61472,1124.9762"/>
</g>
<g style="fill:rgb(83,93,102); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M409.08978,1062.115L409.2198,1065.0339Q408.77878,1073.9514,402.34653,1080.0094Q395.86508,1086.0688,386.1481,1086.1858Q376.82382,1085.8582,370.55408,1079.7728Q368.54297,1077.804,367.10776,1075.3741L365.84607,1072.8514Q364.74313,1070.2247,364.3934,1067.2711Q363.58707,1058.1761,369.1985,1051.4279Q374.8099,1044.6794,384.28003,1043.3551Q393.803,1042.4884,400.8036,1047.7819Q407.80414,1053.0753,409.08978,1062.115"/>
</g>
<g style="fill:rgb(83,93,102); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M383.63303,1074.1713L387.34592,1072.1935L391.0588,1070.2157Q401.51904,1089.8469,411.97928,1109.4781Q408.26685,1111.4569,404.55438,1113.4355L383.63303,1074.1713"/>
</g>
<g style="fill:rgb(83,93,102); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M220.84837,873.0063L220.8485,873.0068L220.84862,873.00726L220.8485,873.0068L220.84837,873.0063L220.8485,873.0068L220.84862,873.00726Q220.8485,873.0068,220.84837,873.0063"/>
</g>
<g style="fill:rgb(83,93,102); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M400.40652,1100.9554L409.52805,1100.5149L418.6496,1100.0746Q419.7661,1123.1688,420.8826,1146.263Q411.7611,1146.7045,402.63962,1147.146Q401.5231,1124.0507,400.40652,1100.9554"/>
</g>
<g style="fill:rgb(83,93,102); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M263.7017,860.01697L263.70172,860.01746L263.70178,860.01794L263.70172,860.01746L263.7017,860.01697L263.70172,860.01746L263.70178,860.01794Q263.70172,860.01746,263.7017,860.01697"/>
</g>
<g style="fill:rgb(83,93,102); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M263.7017,860.01697L263.70172,860.01746L263.70178,860.01794L263.70172,860.01746L263.7017,860.01697L263.70172,860.01746L263.70178,860.01794Q263.70172,860.01746,263.7017,860.01697"/>
</g>
<g style="fill:rgb(64,74,83); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M132.51271,1193.7445L516.1133,1193.745L899.71387,1193.7455Q899.71387,1207.9309,899.71387,1222.1165Q516.1133,1222.117,132.51271,1222.1174Q132.51271,1207.9309,132.51271,1193.7445"/>
</g>
<g style="fill:rgb(255,0,0); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M649.0453,160.0122L649.04504,160.01385L649.0453,160.0122L649.04504,160.01385"/>
</g>
<g style="fill:rgb(7,2,0); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M515.32825,428.29352L515.32825,428.29382L515.3283,428.2941L515.32825,428.29352"/>
</g>
<g style="fill:rgb(58,105,116); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M266.85712,540.0612L263.3409,544.8619L231.49304,588.04333Q222.83252,601.02893,219.75522,607.6643Q208.21323,631.8334,208.31346,650.53094Q207.78188,666.0852,214.3094,681.9117Q215.07404,684.54944,222.30873,697.5908Q243.32623,731.2248,243.953,733.58923Q245.6877,736.15436,250.31866,748.6748Q256.05988,763.7133,256.74518,797.85754L361.37473,775.69965Q363.35208,747.67334,359.68445,732.6364Q356.84778,717.7822,341.86176,686.02606L370.23553,624.30347L266.85712,540.0612"/>
</g>
<g style="fill:rgb(255,0,10); fill-opacity:0.99607843; stroke-linejoin: miter">
<path d="M439.85065,744.0698L439.85046,744.0704L439.8503,744.0709L439.85065,744.0698"/>
</g>
<g style="fill:rgb(255,255,255); fill-opacity:1.0; stroke-linejoin: miter">
<path d="M385.44113,339.73096L386.9223,337.052Q392.61926,326.78458,393.16736,323.34714Q393.52423,322.44327,394.9854,323.00836Q396.28058,323.27728,406.3688,330.265L446.375,358.45892L439.7273,372.29102L464.63013,399.5638Q491.59344,444.8431,506.27737,458.76483L455.76358,511.07495L442.6478,495.85095Q425.414,535.68896,415.35794,551.0137Q406.25183,566.3236,377.232,600.0769Q371.43304,608.44336,371.4427,609.55383Q365.362,621.37683,384.79248,631.196Q325.40927,639.03357,296.0638,630.8838Q268.56412,624.89343,222.07092,588.79816Q267.61932,531.65607,290.79724,492.14575L304.5493,465.86725L332.25858,402.10638Q340.41354,385.72247,342.29547,383.50732Q358.2011,359.02917,385.44113,339.73096"/>
</g>
</g>
</svg>