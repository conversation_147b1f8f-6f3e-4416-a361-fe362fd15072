<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="800" height="600">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#5e72e4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#825ee4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="8" />
      <feOffset dx="0" dy="4" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect width="800" height="600" fill="url(#grad2)" rx="30" ry="30" />
  
  <!-- Decorative Elements -->
  <circle cx="150" cy="120" r="80" fill="url(#grad1)" opacity="0.1" />
  <circle cx="700" cy="500" r="100" fill="url(#grad1)" opacity="0.1" />
  <circle cx="250" cy="520" r="60" fill="url(#grad1)" opacity="0.05" />
  <circle cx="650" cy="150" r="70" fill="url(#grad1)" opacity="0.05" />
  
  <!-- User Icon with shadow -->
  <g transform="translate(400, 300)" filter="url(#shadow)">
    <!-- Avatar Circle Background -->
    <circle cx="0" cy="0" r="150" fill="url(#grad1)" />
    
    <!-- User Silhouette - More modern and simplified -->
    <g fill="#ffffff">
      <!-- Head -->
      <circle cx="0" cy="-40" r="60" />
      
      <!-- Body - Smoother shape -->
      <path d="M-80,100 C-80,20 80,20 80,100 Q80,140 0,160 Q-80,140 -80,100 Z" />
    </g>
  </g>
</svg> 