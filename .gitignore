target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/build/

### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/
nb-configuration.xml

### VS Code ###
.vscode/

### Mac OS ###
.DS_Store

# Application configuration
src/main/resources/app.properties

# Tomcat context file (optional, may contain env-specific config)
src/main/webapp/META-INF/context.xml

# Logs
*.log
logs/

# OS files
Thumbs.db