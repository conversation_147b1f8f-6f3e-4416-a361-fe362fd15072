<?xml version="1.0" encoding="UTF-8"?>
<web-app version="5.0" xmlns="https://jakarta.ee/xml/ns/jakartaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee https://jakarta.ee/xml/ns/jakartaee/web-app_5_0.xsd">
    <!-- UTF-8 Encoding Filter -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>org.apache.catalina.filters.SetCharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>
    <error-page>
        <error-code>404</error-code>
        <location>/error404.jsp</location>
    </error-page>
    <!-- LoginController -->
    <servlet>
        <servlet-name>login</servlet-name>
        <servlet-class>Controllers.LoginController</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>login</servlet-name>
        <url-pattern>/login</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>forgot-password</servlet-name>
        <servlet-class>Controllers.ForgotPasswordController</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>forgot-password</servlet-name>
        <url-pattern>/forgot-password</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>reset-password</servlet-name>
        <servlet-class>Controllers.ResetPasswordController</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>reset-password</servlet-name>
        <url-pattern>/reset-password</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>profile</servlet-name>
        <servlet-class>Controllers.ProfileController</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>profile</servlet-name>
        <url-pattern>/profile</url-pattern>
    </servlet-mapping>
    <!-- register.jsp -->
    <servlet>
        <servlet-name>register</servlet-name>
        <jsp-file>/register.jsp</jsp-file>
    </servlet>
    <servlet-mapping>
        <servlet-name>register</servlet-name>
        <url-pattern>/register</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>verify-email</servlet-name>
        <jsp-file>/verify-email.jsp</jsp-file>
    </servlet>
    <servlet-mapping>
        <servlet-name>verify-email</servlet-name>
        <url-pattern>/verify-email</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>VerifyEmail</servlet-name>
        <servlet-class>Controllers.VerifyEmail</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>VerifyEmail</servlet-name>
        <url-pattern>/VerifyEmail</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>dashboard</servlet-name>
        <jsp-file>/dashboard.jsp</jsp-file>
    </servlet>
    <servlet-mapping>
        <servlet-name>dashboard</servlet-name>
        <url-pattern>/dashboard</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>member-dashboard</servlet-name>
        <jsp-file>/member-dashboard.jsp</jsp-file>
    </servlet>
    <servlet-mapping>
        <servlet-name>member-dashboard</servlet-name>
        <url-pattern>/member-dashboard</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AddPackageServlet</servlet-name>
        <servlet-class>Controllers.AddPackageServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AddPackageServlet</servlet-name>
        <url-pattern>/addPackage</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ListPackageServlet</servlet-name>
        <servlet-class>Controllers.ListPackageServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ListPackageServlet</servlet-name>
        <url-pattern>/listPackage</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>EditPackageServlet</servlet-name>
        <servlet-class>Controllers.EditPackageServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EditPackageServlet</servlet-name>
        <url-pattern>/editPackage</url-pattern>
    </servlet-mapping>
    <!-- Servlet cập nhật trạng thái gói tập -->
    <servlet>
        <servlet-name>UpdatePackageStatusServlet</servlet-name>
        <servlet-class>Controllers.UpdatePackageStatusServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UpdatePackageStatusServlet</servlet-name>
        <url-pattern>/updatePackageStatus</url-pattern>
    </servlet-mapping>
    <!-- VoucherController mapped to /voucher -->
    <servlet>
        <servlet-name>VoucherController</servlet-name>
        <servlet-class>Controllers.VoucherController</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>VoucherController</servlet-name>
        <url-pattern>/voucher</url-pattern>
    </servlet-mapping>

    <!-- Member pages -->
    <servlet>
        <servlet-name>member-packages</servlet-name>
        <jsp-file>/member-packages.jsp</jsp-file>
    </servlet>
    <servlet-mapping>
        <servlet-name>member-packages</servlet-name>
        <url-pattern>/member-packages-view</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>all-packages</servlet-name>
        <jsp-file>/all-packages.jsp</jsp-file>
    </servlet>
    <servlet-mapping>
        <servlet-name>all-packages</servlet-name>
        <url-pattern>/all-packages-view</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>member-schedule</servlet-name>
        <jsp-file>/member-schedule.jsp</jsp-file>
    </servlet>
    <servlet-mapping>
        <servlet-name>member-schedule</servlet-name>
        <url-pattern>/member-schedule</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>member-shop</servlet-name>
        <jsp-file>/member-shop.jsp</jsp-file>
    </servlet>
    <servlet-mapping>
        <servlet-name>member-shop</servlet-name>
        <url-pattern>/member-shop</url-pattern>
    </servlet-mapping>

    <!-- Removing member-cart servlet and servlet-mapping to resolve conflict with CartController -->

    <!-- Removing member-feedback servlet and servlet-mapping to resolve conflict with
    MemberFeedbackController -->

    <servlet>
        <servlet-name>OrderController</servlet-name>
        <servlet-class>Controllers.OrderController</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OrderController</servlet-name>
        <url-pattern>/order</url-pattern>
    </servlet-mapping>

    <!-- Authentication Filter -->
    <filter>
        <filter-name>AuthenticationFilter</filter-name>
        <filter-class>Filters.AuthenticationFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>AuthenticationFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

</web-app>
